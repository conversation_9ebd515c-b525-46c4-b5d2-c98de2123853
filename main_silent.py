#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج المحاسبة للمكتب الهندسي - تشغيل صامت
تشغيل البرنامج بدون رسائل تقنية في وحدة التحكم
"""

import sys
import os
from contextlib import redirect_stdout, redirect_stderr
from io import StringIO

# إعادة توجيه المخرجات لتجنب الرسائل التقنية
sys.stdout = StringIO()
sys.stderr = StringIO()

try:
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtCore import Qt
    from ui.main_window import MainWindow
    from database import init_db
    import performance_optimizer
    
    def main():
        """تشغيل البرنامج الرئيسي بصمت"""
        # تطبيق تحسينات الأداء
        performance_optimizer.apply_optimizations()
        
        # تهيئة قاعدة البيانات
        init_db()
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setApplicationName("برنامج المحاسبة للمكتب الهندسي")
        app.setApplicationVersion("2.0")
        
        # تطبيق إعدادات عامة للتطبيق
        app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # إنشاء النافذة الرئيسية
        window = MainWindow()
        window.show()
        
        # تشغيل التطبيق
        sys.exit(app.exec_())

    if __name__ == "__main__":
        main()
        
except Exception as e:
    # في حالة حدوث خطأ، إعادة توجيه المخرجات للعرض
    sys.stdout = sys.__stdout__
    sys.stderr = sys.__stderr__
    print(f"خطأ في تشغيل البرنامج: {e}")
    print("يرجى المحاولة مرة أخرى أو استخدام main.py للحصول على تفاصيل أكثر")
