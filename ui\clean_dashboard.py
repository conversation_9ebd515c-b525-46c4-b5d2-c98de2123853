"""
لوحة المعلومات المتطورة النظيفة - بدون أكواد غير مستخدمة
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QFrame, QProgressBar, QScrollArea, QPushButton,
                             QMessageBox, QSizePolicy)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont
from ui.unified_styles import UnifiedStyles
from database import get_session, Client, Supplier, Employee, Project, Invoice, Revenue, Expense, Sale, Purchase, InventoryItem
import datetime
import random


class CleanAdvancedDashboard(QWidget):
    """لوحة المعلومات المتطورة النظيفة"""

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        # التخطيط الرئيسي - استغلال كامل المساحة
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:vertical {
                background: rgba(0, 0, 0, 0.1);
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: rgba(0, 0, 0, 0.3);
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: rgba(0, 0, 0, 0.5);
            }
        """)

        # الحاوية الرئيسية للمحتوى - استغلال كامل مع حفظ المكونات
        content_widget = QWidget()
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش لاستغلال الفراغ
        content_layout.setSpacing(15)  # الحفاظ على المسافات بين المكونات

        # تطبيق التصميم الأنيق
        self.apply_elegant_styling()

        # إنشاء الرسم البياني المتطور الوحيد الشامل
        self.create_ultra_advanced_single_chart(content_layout)

        # إضافة زر البيانات التجريبية
        self.create_sample_data_button(content_layout)

        content_widget.setLayout(content_layout)
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)
        self.setLayout(main_layout)

        # بدء تحديث التاريخ وآخر تحديث
        self.update_datetime()
        self.update_last_update_time()

        # مؤقت التاريخ (تحديث أبطأ لتجنب الوميض)
        self.date_timer = QTimer()
        self.date_timer.timeout.connect(self.update_datetime)
        self.date_timer.start(300000)  # تحديث كل 5 دقائق

        # مؤقت آخر تحديث (تحديث أبطأ لتجنب الوميض)
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_last_update_time)
        self.update_timer.start(120000)  # تحديث كل دقيقتين

        print("✅ تم إنشاء الواجهة المتطورة النظيفة بنجاح!")

    def create_sample_data_button(self, layout):
        """إنشاء زر إضافة البيانات التجريبية"""
        # إطار الزر
        button_frame = QFrame()
        button_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.9),
                    stop:0.4 rgba(241, 245, 249, 0.85),
                    stop:0.6 rgba(248, 250, 252, 0.9),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid #000000;
                border-radius: 15px;
                margin: 5px;
                padding: 10px;
                max-height: 80px;
                min-height: 70px;
            }
        """)

        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(10, 5, 10, 5)
        button_layout.setSpacing(10)

        # زر إضافة البيانات التجريبية
        self.sample_data_button = QPushButton("🎲 إضافة 20 صف بيانات تجريبية لجميع الأقسام")
        self.sample_data_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 193, 7, 0.9),
                    stop:0.2 rgba(255, 235, 59, 0.85),
                    stop:0.4 rgba(255, 241, 118, 0.8),
                    stop:0.6 rgba(255, 235, 59, 0.85),
                    stop:0.8 rgba(255, 193, 7, 0.9),
                    stop:1 rgba(255, 152, 0, 0.85));
                border: 3px solid rgba(255, 152, 0, 0.8);
                border-radius: 12px;
                padding: 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: #1a1a1a;
                text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
                min-height: 45px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 152, 0, 0.95),
                    stop:0.2 rgba(255, 193, 7, 0.9),
                    stop:0.4 rgba(255, 235, 59, 0.85),
                    stop:0.6 rgba(255, 193, 7, 0.9),
                    stop:0.8 rgba(255, 152, 0, 0.95),
                    stop:1 rgba(255, 87, 34, 0.9));
                border: 4px solid rgba(255, 87, 34, 0.9);
                transform: scale(1.02);
                box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 87, 34, 0.9),
                    stop:0.5 rgba(255, 152, 0, 0.85),
                    stop:1 rgba(255, 193, 7, 0.8));
                border: 3px solid rgba(255, 87, 34, 0.95);
                transform: scale(0.98);
            }
        """)
        self.sample_data_button.clicked.connect(self.add_sample_data_to_all_sections)
        self.sample_data_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        button_layout.addWidget(self.sample_data_button)
        button_frame.setLayout(button_layout)
        layout.addWidget(button_frame)

    def add_sample_data_to_all_sections(self):
        """إضافة بيانات تجريبية لجميع الأقسام"""
        try:
            # رسالة تأكيد
            reply = QMessageBox.question(
                self,
                "إضافة بيانات تجريبية",
                "هل تريد إضافة 20 صف بيانات تجريبية لجميع الأقسام؟\n\n"
                "سيتم إضافة بيانات لـ:\n"
                "• العملاء (20 عميل)\n"
                "• الموردين (20 مورد)\n"
                "• الموظفين (20 موظف)\n"
                "• المشاريع (20 مشروع)\n"
                "• الفواتير (20 فاتورة)\n"
                "• الإيرادات (20 إيراد)\n"
                "• المصروفات (20 مصروف)\n"
                "• المبيعات (20 عملية بيع)\n"
                "• المشتريات (20 عملية شراء)\n"
                "• المخزون (20 صنف)\n\n"
                "⚠️ تحذير: هذه العملية قد تستغرق بعض الوقت",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                # تعطيل الزر أثناء العملية
                self.sample_data_button.setEnabled(False)
                self.sample_data_button.setText("⏳ جاري إضافة البيانات...")

                # إضافة البيانات
                success_count = 0
                total_sections = 10

                session = get_session()
                try:
                    # إضافة العملاء
                    if self.add_sample_clients(session):
                        success_count += 1

                    # إضافة الموردين
                    if self.add_sample_suppliers(session):
                        success_count += 1

                    # إضافة الموظفين
                    if self.add_sample_employees(session):
                        success_count += 1

                    # إضافة المشاريع
                    if self.add_sample_projects(session):
                        success_count += 1

                    # إضافة الفواتير
                    if self.add_sample_invoices(session):
                        success_count += 1

                    # إضافة الإيرادات
                    if self.add_sample_revenues(session):
                        success_count += 1

                    # إضافة المصروفات
                    if self.add_sample_expenses(session):
                        success_count += 1

                    # إضافة المبيعات
                    if self.add_sample_sales(session):
                        success_count += 1

                    # إضافة المشتريات
                    if self.add_sample_purchases(session):
                        success_count += 1

                    # إضافة المخزون
                    if self.add_sample_inventory(session):
                        success_count += 1

                    session.commit()

                    # رسالة النجاح
                    QMessageBox.information(
                        self,
                        "تم بنجاح",
                        f"تم إضافة البيانات التجريبية بنجاح!\n\n"
                        f"✅ تم إضافة البيانات لـ {success_count} من أصل {total_sections} أقسام\n"
                        f"📊 إجمالي البيانات المضافة: {success_count * 20} صف\n\n"
                        "يمكنك الآن الانتقال إلى الأقسام المختلفة لمشاهدة البيانات الجديدة."
                    )

                except Exception as e:
                    session.rollback()
                    QMessageBox.critical(
                        self,
                        "خطأ",
                        f"حدث خطأ أثناء إضافة البيانات:\n{str(e)}"
                    )
                finally:
                    session.close()
                    # إعادة تفعيل الزر
                    self.sample_data_button.setEnabled(True)
                    self.sample_data_button.setText("🎲 إضافة 20 صف بيانات تجريبية لجميع الأقسام")

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ غير متوقع: {str(e)}"
            )

    def add_sample_clients(self, session):
        """إضافة 20 عميل تجريبي"""
        try:
            clients_data = [
                ("شركة الأمل للمقاولات", "0112345001", "<EMAIL>", "الرياض - حي الملك فهد", 125000, "شركة مقاولات كبرى"),
                ("مؤسسة النجاح التجارية", "0126789002", "<EMAIL>", "جدة - شارع التحلية", 87500, "مؤسسة تجارية متخصصة"),
                ("عبدالرحمن محمد الخليفي", "0501122003", "<EMAIL>", "الدمام - حي الفردوس", -15000, "مالك عقارات"),
                ("مجموعة الرؤية العقارية", "0920001004", "<EMAIL>", "الرياض - طريق الملك عبدالعزيز", 245000, "مجموعة عقارية"),
                ("فهد سعد المالكي", "0555123005", "<EMAIL>", "مكة - حي العتيبية", 32000, "مستثمر عقاري"),
                ("شركة البناء الحديث", "0138765006", "<EMAIL>", "الخبر - الكورنيش الشمالي", 156000, "شركة إنشاءات"),
                ("نورا محمد العبدالله", "0566789007", "<EMAIL>", "الرياض - حي النرجس", 18500, "مالكة عقارات"),
                ("مؤسسة الصقر الذهبي", "0114567008", "<EMAIL>", "الرياض - حي السليمانية", -8500, "مقاول معتمد"),
                ("خالد عبدالعزيز الراشد", "0503456009", "<EMAIL>", "جدة - حي الزهراء", 67000, "رجل أعمال"),
                ("شركة الخليج للتطوير", "0920012010", "<EMAIL>", "الدمام - حي الشاطئ", 189000, "شركة تطوير عقاري"),
                ("سارة أحمد الغامدي", "0577890011", "<EMAIL>", "الطائف - حي الشفا", 25000, "مهندسة معمارية"),
                ("مجمع الأندلس التجاري", "0126543012", "<EMAIL>", "جدة - طريق الملك عبدالله", -22000, "مجمع تجاري"),
                ("عبدالله محمد الحربي", "0544321013", "<EMAIL>", "الرياض - حي الورود", 95000, "مستثمر كبير"),
                ("مؤسسة التقدم للتجارة", "0138901014", "<EMAIL>", "الخبر - شارع الملك فهد", 43000, "مؤسسة تجارية"),
                ("منى سالم القرني", "0599876015", "<EMAIL>", "أبها - حي المنهل", 12000, "مصممة داخلية"),
                ("شركة الفجر الجديد", "0117654016", "<EMAIL>", "الرياض - الدائري الشرقي", 178000, "شركة مقاولات"),
                ("أحمد سليمان الزهراني", "0512345017", "<EMAIL>", "جدة - حي الروضة", 38000, "مهندس استشاري"),
                ("مجموعة الواحة العقارية", "0920098018", "<EMAIL>", "المدينة - شارع قباء", 78000, "مجموعة عقارية"),
                ("فاطمة عبدالله الشهري", "0588123019", "<EMAIL>", "الرياض - حي الحمراء", -5500, "مصممة معمارية"),
                ("شركة الأصالة للإنشاء", "0133456020", "<EMAIL>", "الدمام - حي الفيصلية", 134000, "شركة إنشاءات")
            ]

            for name, phone, email, address, balance, notes in clients_data:
                # التحقق من عدم وجود العميل
                existing = session.query(Client).filter(Client.name == name).first()
                if not existing:
                    client = Client(
                        name=name,
                        phone=phone,
                        email=email,
                        address=address,
                        balance=balance,
                        notes=notes
                    )
                    session.add(client)

            return True
        except Exception as e:
            print(f"خطأ في إضافة العملاء: {e}")
            return False

    def add_sample_suppliers(self, session):
        """إضافة 20 مورد تجريبي"""
        try:
            suppliers_data = [
                ("شركة مواد البناء المتطورة", "0112001001", "<EMAIL>", "الرياض - المنطقة الصناعية", 45000, "مورد مواد بناء"),
                ("مؤسسة الحديد والصلب", "0126002002", "<EMAIL>", "جدة - المنطقة الصناعية", -12000, "مورد حديد وصلب"),
                ("شركة الأسمنت الوطنية", "0138003003", "<EMAIL>", "الدمام - المنطقة الصناعية", 78000, "مورد أسمنت"),
                ("مؤسسة الكهرباء والإنارة", "0114004004", "<EMAIL>", "الرياض - حي الصناعة", 23000, "مورد كهربائيات"),
                ("شركة السباكة الحديثة", "0555005005", "<EMAIL>", "جدة - حي الجامعة", 34000, "مورد أدوات سباكة"),
                ("مؤسسة الدهانات والعوازل", "0501006006", "<EMAIL>", "الخبر - المنطقة التجارية", -8000, "مورد دهانات"),
                ("شركة الأخشاب المستوردة", "0566007007", "<EMAIL>", "الرياض - طريق الخرج", 56000, "مورد أخشاب"),
                ("مؤسسة البلاط والسيراميك", "0577008008", "<EMAIL>", "الطائف - المنطقة الصناعية", 41000, "مورد بلاط"),
                ("شركة الزجاج والألمنيوم", "0588009009", "<EMAIL>", "الدمام - حي الصناعة", 67000, "مورد زجاج وألمنيوم"),
                ("مؤسسة المعدات الثقيلة", "0599010010", "<EMAIL>", "الرياض - طريق الدمام", 189000, "مورد معدات ثقيلة"),
                ("شركة الرخام والجرانيت", "0511011011", "<EMAIL>", "جدة - طريق مكة", 73000, "مورد رخام وجرانيت"),
                ("مؤسسة التكييف والتبريد", "0522012012", "<EMAIL>", "الخبر - حي الثقبة", -15000, "مورد أنظمة تكييف"),
                ("شركة الأبواب والنوافذ", "0533013013", "<EMAIL>", "الرياض - حي الشفا", 29000, "مورد أبواب ونوافذ"),
                ("مؤسسة الأدوات الصحية", "0544014014", "<EMAIL>", "مكة - حي العزيزية", 38000, "مورد أدوات صحية"),
                ("شركة العوازل المائية", "0555015015", "<EMAIL>", "الطائف - حي السلامة", 22000, "مورد عوازل مائية"),
                ("مؤسسة الإضاءة الذكية", "0566016016", "<EMAIL>", "الدمام - الكورنيش", 45000, "مورد إضاءة ذكية"),
                ("شركة الأرضيات الخشبية", "0577017017", "<EMAIL>", "الرياض - حي النخيل", 51000, "مورد أرضيات خشبية"),
                ("مؤسسة الحجر الطبيعي", "0588018018", "<EMAIL>", "أبها - حي المنهل", 33000, "مورد حجر طبيعي"),
                ("شركة المصاعد الحديثة", "0599019019", "<EMAIL>", "جدة - حي الصفا", 125000, "مورد مصاعد"),
                ("مؤسسة الأمان والحماية", "0511020020", "<EMAIL>", "الخبر - حي الراكة", 67000, "مورد أنظمة أمان")
            ]

            for name, phone, email, address, balance, notes in suppliers_data:
                # التحقق من عدم وجود المورد
                existing = session.query(Supplier).filter(Supplier.name == name).first()
                if not existing:
                    supplier = Supplier(
                        name=name,
                        phone=phone,
                        email=email,
                        address=address,
                        balance=balance,
                        notes=notes
                    )
                    session.add(supplier)

            return True
        except Exception as e:
            print(f"خطأ في إضافة الموردين: {e}")
            return False

    def add_sample_employees(self, session):
        """إضافة 20 موظف تجريبي"""
        try:
            employees_data = [
                ("أحمد محمد السعيد", "مهندس مدني", "0501001001", "<EMAIL>", "الرياض - حي الملك فهد", 8000, 2500),
                ("فاطمة علي الزهراني", "مهندسة معمارية", "0502002002", "<EMAIL>", "جدة - حي الصفا", 7500, 1200),
                ("محمد سالم القحطاني", "مشرف موقع", "0503003003", "<EMAIL>", "الدمام - حي الفيصلية", 6000, -500),
                ("نورا خالد العتيبي", "محاسبة", "0504004004", "<EMAIL>", "مكة - العزيزية", 5500, 800),
                ("عبدالله أحمد الغامدي", "مهندس كهرباء", "0505005005", "<EMAIL>", "المدينة - حي العوالي", 7000, 1500),
                ("سارة عبدالرحمن النجار", "مصممة داخلية", "0506006006", "<EMAIL>", "الطائف - حي الشفا", 6500, 300),
                ("خالد عبدالعزيز الحربي", "مدير مشروع", "0507007007", "<EMAIL>", "الرياض - حي الملز", 9000, 3200),
                ("منى سعد الدوسري", "مهندسة ديكور", "0508008008", "<EMAIL>", "الخبر - حي الراكة", 6800, 900),
                ("يوسف محمد العمري", "فني كهرباء", "0509009009", "<EMAIL>", "جدة - حي البلد", 4500, -200),
                ("هند علي الشهري", "سكرتيرة تنفيذية", "0510010010", "<EMAIL>", "أبها - حي المنهل", 4000, 600),
                ("عمر حسن البقمي", "مهندس ميكانيكا", "0511011011", "<EMAIL>", "الباحة - حي الزاهر", 7200, 1800),
                ("ريم فهد القرشي", "مديرة مالية", "0512012012", "<EMAIL>", "مكة - حي الشوقية", 8500, 2800),
                ("طارق عبدالله الزهراني", "مساح أراضي", "0513013013", "<EMAIL>", "الطائف - حي السلامة", 5000, 400),
                ("لينا أحمد الغامدي", "مهندسة بيئة", "0514014014", "<EMAIL>", "الرياض - حي العليا", 7800, 2100),
                ("سلطان محمد العتيبي", "مقاول باطن", "0515015015", "<EMAIL>", "الدمام - حي الشاطئ", 6200, 1000),
                ("نادية سالم الحارثي", "مهندسة إنشائية", "0516016016", "<EMAIL>", "جدة - حي الحمراء", 7600, 1700),
                ("فيصل عبدالرحمن السبيعي", "مدير تنفيذي", "0517017017", "<EMAIL>", "الخرج - حي النهضة", 9500, 3500),
                ("أمل خالد الشمري", "محللة مالية", "0518018018", "<EMAIL>", "حائل - حي الوسيطاء", 6000, 1200),
                ("بدر سعود المطيري", "مهندس تخطيط", "0519019019", "<EMAIL>", "الرياض - حي الصحافة", 8200, 2600),
                ("وفاء عبدالله القحطاني", "مديرة موارد بشرية", "0520020020", "<EMAIL>", "الخبر - حي الثقبة", 7400, 1900)
            ]

            for name, position, phone, email, address, salary, balance in employees_data:
                # التحقق من عدم وجود الموظف
                existing = session.query(Employee).filter(Employee.name == name).first()
                if not existing:
                    employee = Employee(
                        name=name,
                        position=position,
                        phone=phone,
                        email=email,
                        address=address,
                        salary=salary,
                        balance=balance,
                        notes=f"موظف {position} - تم التعيين حديثاً"
                    )
                    session.add(employee)

            return True
        except Exception as e:
            print(f"خطأ في إضافة الموظفين: {e}")
            return False

    def add_sample_projects(self, session):
        """إضافة 20 مشروع تجريبي"""
        try:
            projects_data = [
                ("مجمع الأعمال التجاري", "الرياض - حي الملك فهد", 2500.0, "in_progress", 1500000, 1200000),
                ("برج السكن الفاخر", "جدة - الكورنيش الشمالي", 1800.0, "planning", 2200000, 800000),
                ("مول التسوق الحديث", "الدمام - شارع الملك فهد", 3200.0, "in_progress", 3500000, 2800000),
                ("مجمع الفلل السكنية", "الرياض - حي النرجس", 5000.0, "completed", 4200000, 4100000),
                ("مركز الأعمال الطبي", "مكة - حي العتيبية", 1200.0, "in_progress", 1800000, 1400000),
                ("مشروع الواجهة البحرية", "الخبر - الكورنيش", 800.0, "planning", 2800000, 500000),
                ("مجمع المكاتب الإدارية", "الرياض - طريق الملك عبدالعزيز", 2000.0, "in_progress", 2600000, 2100000),
                ("فندق الضيافة الفاخر", "الطائف - حي الشفا", 1500.0, "planning", 3200000, 900000),
                ("مركز التسوق العائلي", "أبها - حي المنهل", 2200.0, "in_progress", 2400000, 1800000),
                ("مشروع الأبراج السكنية", "جدة - حي الزهراء", 3500.0, "completed", 5200000, 5100000),
                ("مجمع المدارس الحديث", "الدمام - حي الفيصلية", 4000.0, "in_progress", 3800000, 3200000),
                ("مركز الرعاية الصحية", "المدينة - حي العوالي", 1000.0, "planning", 2100000, 600000),
                ("مشروع القرية التراثية", "الباحة - حي الزاهر", 1800.0, "in_progress", 1900000, 1500000),
                ("مجمع الشقق الاقتصادية", "حائل - حي الوسيطاء", 2800.0, "planning", 2200000, 800000),
                ("مركز المؤتمرات الدولي", "الرياض - حي الدبلوماسيين", 2500.0, "in_progress", 4500000, 3800000),
                ("مشروع المنتجع السياحي", "العلا - المنطقة التاريخية", 6000.0, "planning", 8500000, 2200000),
                ("مجمع الورش الصناعية", "الجبيل - المنطقة الصناعية", 3000.0, "in_progress", 2800000, 2400000),
                ("مركز التدريب المهني", "الخرج - حي النهضة", 1600.0, "completed", 1800000, 1750000),
                ("مشروع الحديقة العامة", "تبوك - وسط المدينة", 2000.0, "in_progress", 1200000, 900000),
                ("مجمع الخدمات الحكومية", "جازان - حي الصفا", 1400.0, "planning", 2600000, 700000)
            ]

            # الحصول على عملاء موجودين لربط المشاريع بهم
            clients = session.query(Client).limit(20).all()

            for i, (name, location, area, status, budget, total_cost) in enumerate(projects_data):
                # التحقق من عدم وجود المشروع
                existing = session.query(Project).filter(Project.name == name).first()
                if not existing:
                    # ربط المشروع بعميل إذا كان متوفراً
                    client_id = clients[i % len(clients)].id if clients else None

                    project = Project(
                        name=name,
                        client_id=client_id,
                        location=location,
                        area=area,
                        status=status,
                        budget=budget,
                        total_cost=total_cost,
                        description=f"مشروع {name} في {location}",
                        notes=f"مشروع {status} - مساحة {area} متر مربع"
                    )
                    session.add(project)

            return True
        except Exception as e:
            print(f"خطأ في إضافة المشاريع: {e}")
            return False

    def add_sample_invoices(self, session):
        """إضافة 20 فاتورة تجريبية"""
        try:
            # الحصول على عملاء موجودين
            clients = session.query(Client).limit(20).all()
            if not clients:
                return False

            invoices_data = [
                ("INV-2024-001", 125000, 100000, "paid"),
                ("INV-2024-002", 87500, 87500, "paid"),
                ("INV-2024-003", 156000, 50000, "partially_paid"),
                ("INV-2024-004", 245000, 0, "pending"),
                ("INV-2024-005", 32000, 32000, "paid"),
                ("INV-2024-006", 78000, 78000, "paid"),
                ("INV-2024-007", 189000, 120000, "partially_paid"),
                ("INV-2024-008", 67000, 0, "pending"),
                ("INV-2024-009", 134000, 134000, "paid"),
                ("INV-2024-010", 95000, 45000, "partially_paid"),
                ("INV-2024-011", 43000, 43000, "paid"),
                ("INV-2024-012", 178000, 0, "pending"),
                ("INV-2024-013", 56000, 56000, "paid"),
                ("INV-2024-014", 89000, 30000, "partially_paid"),
                ("INV-2024-015", 112000, 112000, "paid"),
                ("INV-2024-016", 167000, 80000, "partially_paid"),
                ("INV-2024-017", 234000, 234000, "paid"),
                ("INV-2024-018", 45000, 0, "pending"),
                ("INV-2024-019", 198000, 150000, "partially_paid"),
                ("INV-2024-020", 76000, 76000, "paid")
            ]

            for i, (invoice_number, total_amount, paid_amount, status) in enumerate(invoices_data):
                # التحقق من عدم وجود الفاتورة
                existing = session.query(Invoice).filter(Invoice.invoice_number == invoice_number).first()
                if not existing:
                    client = clients[i % len(clients)]

                    invoice = Invoice(
                        invoice_number=invoice_number,
                        client_id=client.id,
                        total_amount=total_amount,
                        paid_amount=paid_amount,
                        status=status,
                        notes=f"فاتورة {status} للعميل {client.name}"
                    )
                    session.add(invoice)

            return True
        except Exception as e:
            print(f"خطأ في إضافة الفواتير: {e}")
            return False

    def add_sample_revenues(self, session):
        """إضافة 20 إيراد تجريبي"""
        try:
            # الحصول على عملاء وفواتير موجودة
            clients = session.query(Client).limit(20).all()
            if not clients:
                return False

            revenues_data = [
                ("دفعة من العميل الأول", 125000, "project_payment"),
                ("استلام مقدم مشروع", 50000, "advance_payment"),
                ("دفعة نهائية مشروع", 87500, "final_payment"),
                ("دفعة جزئية", 30000, "partial_payment"),
                ("استلام مستحقات", 156000, "dues_collection"),
                ("دفعة مشروع سكني", 245000, "project_payment"),
                ("استلام عمولة", 15000, "commission"),
                ("دفعة استشارات", 32000, "consultation_fee"),
                ("استلام مقاولة", 189000, "contracting_fee"),
                ("دفعة تصميم", 25000, "design_fee"),
                ("استلام إشراف", 67000, "supervision_fee"),
                ("دفعة تنفيذ", 134000, "execution_payment"),
                ("استلام صيانة", 18000, "maintenance_fee"),
                ("دفعة مواد", 95000, "materials_payment"),
                ("استلام خدمات", 43000, "services_fee"),
                ("دفعة معدات", 78000, "equipment_rental"),
                ("استلام نقل", 12000, "transportation_fee"),
                ("دفعة عمالة", 56000, "labor_payment"),
                ("استلام إضافي", 89000, "additional_work"),
                ("دفعة ختامية", 167000, "final_settlement")
            ]

            for i, (description, amount, revenue_type) in enumerate(revenues_data):
                client = clients[i % len(clients)]

                revenue = Revenue(
                    description=description,
                    amount=amount,
                    client_id=client.id,
                    category=revenue_type,
                    notes=f"إيراد من {client.name} - {description}"
                )
                session.add(revenue)

            return True
        except Exception as e:
            print(f"خطأ في إضافة الإيرادات: {e}")
            return False

    def add_sample_expenses(self, session):
        """إضافة 20 مصروف تجريبي"""
        try:
            expenses_data = [
                ("شراء مواد بناء", 45000, "materials"),
                ("أجور عمالة", 32000, "labor"),
                ("إيجار معدات", 18000, "equipment_rental"),
                ("وقود ونقل", 8500, "transportation"),
                ("كهرباء وماء", 12000, "utilities"),
                ("صيانة معدات", 15000, "maintenance"),
                ("رواتب موظفين", 85000, "salaries"),
                ("تأمينات اجتماعية", 12500, "insurance"),
                ("مصاريف إدارية", 9000, "administrative"),
                ("اتصالات وإنترنت", 3500, "communications"),
                ("مصاريف سفر", 7200, "travel"),
                ("قرطاسية ومكتبية", 2800, "office_supplies"),
                ("إعلان وتسويق", 15000, "marketing"),
                ("استشارات قانونية", 8000, "legal_consultation"),
                ("تدريب موظفين", 6500, "training"),
                ("صيانة مكاتب", 4200, "office_maintenance"),
                ("تأمين مشاريع", 22000, "project_insurance"),
                ("رسوم حكومية", 5500, "government_fees"),
                ("مصاريف بنكية", 1800, "bank_charges"),
                ("مصاريف متنوعة", 3200, "miscellaneous")
            ]

            for description, amount, category in expenses_data:
                expense = Expense(
                    description=description,
                    amount=amount,
                    category=category,
                    notes=f"مصروف {category} - {description}"
                )
                session.add(expense)

            return True
        except Exception as e:
            print(f"خطأ في إضافة المصروفات: {e}")
            return False

    def add_sample_sales(self, session):
        """إضافة 20 عملية بيع تجريبية"""
        try:
            # الحصول على عملاء موجودين
            clients = session.query(Client).limit(20).all()
            if not clients:
                return False

            sales_data = [
                ("SALE-2024-001", 125000, 100000, "completed"),
                ("SALE-2024-002", 87500, 87500, "completed"),
                ("SALE-2024-003", 156000, 50000, "pending"),
                ("SALE-2024-004", 245000, 245000, "completed"),
                ("SALE-2024-005", 32000, 0, "cancelled"),
                ("SALE-2024-006", 78000, 78000, "completed"),
                ("SALE-2024-007", 189000, 120000, "pending"),
                ("SALE-2024-008", 67000, 67000, "completed"),
                ("SALE-2024-009", 134000, 134000, "completed"),
                ("SALE-2024-010", 95000, 45000, "pending"),
                ("SALE-2024-011", 43000, 43000, "completed"),
                ("SALE-2024-012", 178000, 178000, "completed"),
                ("SALE-2024-013", 56000, 0, "cancelled"),
                ("SALE-2024-014", 89000, 30000, "pending"),
                ("SALE-2024-015", 112000, 112000, "completed"),
                ("SALE-2024-016", 167000, 80000, "pending"),
                ("SALE-2024-017", 234000, 234000, "completed"),
                ("SALE-2024-018", 45000, 45000, "completed"),
                ("SALE-2024-019", 198000, 150000, "pending"),
                ("SALE-2024-020", 76000, 76000, "completed")
            ]

            for i, (sale_number, total_amount, paid_amount, status) in enumerate(sales_data):
                # التحقق من عدم وجود عملية البيع
                existing = session.query(Sale).filter(Sale.sale_number == sale_number).first()
                if not existing:
                    client = clients[i % len(clients)]

                    sale = Sale(
                        sale_number=sale_number,
                        client_id=client.id,
                        total_amount=total_amount,
                        paid_amount=paid_amount,
                        status=status,
                        notes=f"عملية بيع {status} للعميل {client.name}"
                    )
                    session.add(sale)

            return True
        except Exception as e:
            print(f"خطأ في إضافة المبيعات: {e}")
            return False

    def add_sample_purchases(self, session):
        """إضافة 20 عملية شراء تجريبية"""
        try:
            # الحصول على موردين موجودين
            suppliers = session.query(Supplier).limit(20).all()
            if not suppliers:
                return False

            purchases_data = [
                ("PUR-2024-001", 45000, 45000, "completed"),
                ("PUR-2024-002", 32000, 25000, "pending"),
                ("PUR-2024-003", 78000, 78000, "completed"),
                ("PUR-2024-004", 156000, 100000, "pending"),
                ("PUR-2024-005", 23000, 23000, "completed"),
                ("PUR-2024-006", 67000, 0, "cancelled"),
                ("PUR-2024-007", 89000, 89000, "completed"),
                ("PUR-2024-008", 134000, 80000, "pending"),
                ("PUR-2024-009", 56000, 56000, "completed"),
                ("PUR-2024-010", 112000, 112000, "completed"),
                ("PUR-2024-011", 34000, 20000, "pending"),
                ("PUR-2024-012", 189000, 189000, "completed"),
                ("PUR-2024-013", 67000, 67000, "completed"),
                ("PUR-2024-014", 98000, 50000, "pending"),
                ("PUR-2024-015", 145000, 145000, "completed"),
                ("PUR-2024-016", 76000, 0, "cancelled"),
                ("PUR-2024-017", 123000, 123000, "completed"),
                ("PUR-2024-018", 87000, 60000, "pending"),
                ("PUR-2024-019", 167000, 167000, "completed"),
                ("PUR-2024-020", 234000, 150000, "pending")
            ]

            for i, (purchase_number, total_amount, paid_amount, status) in enumerate(purchases_data):
                # التحقق من عدم وجود عملية الشراء
                existing = session.query(Purchase).filter(Purchase.purchase_number == purchase_number).first()
                if not existing:
                    supplier = suppliers[i % len(suppliers)]

                    purchase = Purchase(
                        purchase_number=purchase_number,
                        supplier_id=supplier.id,
                        total_amount=total_amount,
                        paid_amount=paid_amount,
                        status=status,
                        notes=f"عملية شراء {status} من المورد {supplier.name}"
                    )
                    session.add(purchase)

            return True
        except Exception as e:
            print(f"خطأ في إضافة المشتريات: {e}")
            return False

    def add_sample_inventory(self, session):
        """إضافة 20 صنف مخزون تجريبي"""
        try:
            inventory_data = [
                ("أسمنت بورتلاندي", "كيس 50 كيلو", 25.50, 500, 100, "cement"),
                ("حديد تسليح 12 مم", "طن", 2800.00, 50, 10, "steel"),
                ("طوب أحمر", "ألف طوبة", 180.00, 200, 50, "bricks"),
                ("رمل نظيف", "متر مكعب", 45.00, 300, 80, "sand"),
                ("حصى مدرج", "متر مكعب", 55.00, 250, 60, "gravel"),
                ("بلاط سيراميك", "متر مربع", 35.00, 800, 200, "tiles"),
                ("دهان داخلي", "جالون 4 لتر", 85.00, 150, 30, "paint"),
                ("أنابيب PVC", "متر طولي", 12.50, 1000, 300, "pipes"),
                ("كابلات كهرباء", "متر طولي", 8.75, 2000, 500, "cables"),
                ("زجاج شفاف", "متر مربع", 120.00, 100, 25, "glass"),
                ("ألواح جبس", "لوح 120x240", 45.00, 300, 80, "gypsum"),
                ("عوازل مائية", "رول 10 متر", 150.00, 80, 20, "waterproofing"),
                ("أبواب خشبية", "باب كامل", 450.00, 50, 15, "doors"),
                ("نوافذ ألمنيوم", "متر مربع", 180.00, 120, 30, "windows"),
                ("مفاتيح كهرباء", "قطعة", 15.00, 500, 150, "switches"),
                ("مواسير صرف", "متر طولي", 25.00, 400, 100, "drainage"),
                ("رخام طبيعي", "متر مربع", 250.00, 60, 15, "marble"),
                ("مسامير وبراغي", "كيلو", 18.00, 200, 50, "screws"),
                ("لاصق بلاط", "كيس 25 كيلو", 32.00, 150, 40, "adhesive"),
                ("عازل حراري", "متر مربع", 65.00, 180, 45, "insulation")
            ]

            for name, unit, price, quantity, min_quantity, category in inventory_data:
                # التحقق من عدم وجود الصنف
                existing = session.query(InventoryItem).filter(InventoryItem.name == name).first()
                if not existing:
                    item = InventoryItem(
                        name=name,
                        unit=unit,
                        price=price,
                        quantity=quantity,
                        min_quantity=min_quantity,
                        category=category,
                        notes=f"صنف {category} - {name}"
                    )
                    session.add(item)

            return True
        except Exception as e:
            print(f"خطأ في إضافة المخزون: {e}")
            return False

    def update_datetime(self):
        """تحديث التاريخ فقط مع معلومات إضافية"""
        now = datetime.datetime.now()

        # تنسيق التاريخ: سنة/شهر/يوم
        date_str = now.strftime("%Y/%m/%d")

        # أسماء الأيام بالعربية
        arabic_days = ["الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت", "الأحد"]
        day_name = arabic_days[now.weekday()]

        # تحديد الأيقونة حسب اليوم
        day_icons = {
            "الاثنين": "🌟",
            "الثلاثاء": "💫",
            "الأربعاء": "⭐",
            "الخميس": "✨",
            "الجمعة": "🌙",
            "السبت": "☀️",
            "الأحد": "🌅"
        }
        day_icon = day_icons.get(day_name, "📅")

        # عرض التاريخ واليوم فقط (بدون وقت)
        datetime_str = f"{day_icon} {day_name} | 📅 {date_str}"
        self.datetime_label.setText(datetime_str)

    def update_last_update_time(self):
        """تحديث مؤشر آخر تحديث المتطور بدون وقت"""
        now = datetime.datetime.now()

        # تحديد الأيقونة والرسالة حسب الوقت
        hour = now.hour
        if 6 <= hour < 12:
            update_icon = "🌟"
            update_message = "تحديث صباحي"
            status_color = "rgba(255, 193, 7, 0.8)"
        elif 12 <= hour < 18:
            update_icon = "⚡"
            update_message = "تحديث نهاري"
            status_color = "rgba(255, 152, 0, 0.8)"
        elif 18 <= hour < 22:
            update_icon = "🔥"
            update_message = "تحديث مسائي"
            status_color = "rgba(156, 39, 176, 0.8)"
        else:
            update_icon = "💫"
            update_message = "تحديث ليلي"
            status_color = "rgba(63, 81, 181, 0.8)"

        # رسائل متنوعة للتحديث بأيقونات محسنة
        update_messages = [
            f"{update_icon} {update_message} - نشط",
            f"🔄 {update_message} - محدث",
            f"⚡ {update_message} - مباشر",
            f"📈 {update_message} - فعال",
            f"🎯 {update_message} - متاح",
            f"✨ {update_message} - جاهز",
            f"🚀 {update_message} - متطور",
            f"💫 {update_message} - ذكي"
        ]

        # اختيار رسالة عشوائية
        import random
        selected_message = random.choice(update_messages)
        self.last_update_label.setText(selected_message)

        # تحديث لون الخلفية حسب الوقت بدون إطارات - شفاف
        self.last_update_label.setStyleSheet("""
            color: white;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            border: none;
            padding: 8px 16px;
        """)

    def create_ultra_advanced_single_chart(self, layout):
        """إنشاء رسم بياني واحد متطور جداً يحتوي على كل شيء"""
        # الحاوية الرئيسية للرسم البياني المتطور
        main_chart_frame = QFrame()
        main_chart_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #475569, stop:0.8 #334155,
                    stop:0.9 #1E293B, stop:1 #0F172A);
                border: none;
                border-radius: 20px;
                margin: 2px;
            }
        """)

        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش لاستغلال الفراغ
        main_layout.setSpacing(20)  # الحفاظ على المسافات بين الأقسام

        # العنوان الرئيسي المتطور مع ألوان متدرجة بدون إطارات
        title_container = QWidget()
        title_container.setFixedHeight(80)  # تحديد ارتفاع 80 للمساحة الرئيسية
        title_container.setStyleSheet("""
            QWidget {
                background: transparent;
                border: none;
                margin: 2px;
            }
        """)

        title_layout = QHBoxLayout()
        title_layout.setContentsMargins(8, 6, 8, 6)  # هوامش مناسبة للارتفاع 80
        title_layout.setSpacing(8)  # مسافات مناسبة بين العناصر

        # العنوان المتطور مع أفضل أيقونة للوحة التحكم
        main_title = QLabel("⚡ لوحة التحكم الذكية المتطورة - تحليلات شاملة")
        main_title.setFont(QFont("Arial", 24, QFont.Bold))
        main_title.setStyleSheet("""
            color: white;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            border: none;
            padding: 8px 16px;
        """)
        title_layout.addWidget(main_title)

        title_layout.addStretch()

        # التاريخ المتطور بدون إطارات
        self.datetime_label = QLabel()
        self.datetime_label.setFont(QFont("Arial", 24, QFont.Bold))
        self.datetime_label.setStyleSheet("""
            color: white;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(255, 255, 255, 0.2),
                stop:1 rgba(255, 255, 255, 0.1));
            border-radius: 15px;
            border: none;
            padding: 8px 16px;
        """)
        self.datetime_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(self.datetime_label)

        title_layout.addStretch()

        # مؤشر آخر تحديث المتطور بدون إطارات
        self.last_update_label = QLabel()
        self.last_update_label.setFont(QFont("Arial", 24, QFont.Bold))
        self.last_update_label.setStyleSheet("""
            color: white;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            border: none;
            padding: 8px 16px;
        """)
        self.update_last_update_time()
        title_layout.addWidget(self.last_update_label)

        title_container.setLayout(title_layout)
        main_layout.addWidget(title_container)

        # إضافة مساحة مرنة في الأعلى لتوسيط المحتوى
        main_layout.addStretch(1)

        # القسم الأول: الإحصائيات الرئيسية المتطورة (في المنتصف)
        stats_section = self.create_ultra_stats_section()
        main_layout.addWidget(stats_section)

        # إضافة مساحة صغيرة بين الإحصائيات والرسوم البيانية
        main_layout.addSpacing(5)

        # القسم الثاني: الرسوم البيانية التفاعلية المتطورة (في المنتصف)
        charts_section = self.create_ultra_charts_section()
        main_layout.addWidget(charts_section)

        # إضافة مساحة مرنة في الأسفل لتوسيط المحتوى
        main_layout.addStretch(1)

        main_chart_frame.setLayout(main_layout)
        layout.addWidget(main_chart_frame)

    def create_ultra_stats_section(self):
        """إنشاء قسم الإحصائيات المتطور بدون إطارات"""
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background: transparent;
                border: none;
            }
        """)

        stats_layout = QVBoxLayout()
        stats_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش لاستغلال الفراغ
        stats_layout.setSpacing(15)  # الحفاظ على المسافات بين المكونات

        # عنوان القسم المتطور مع أيقونة محسنة
        section_title = QLabel("⚡ الإحصائيات الذكية والتحليلات المتقدمة")
        section_title.setFont(QFont("Arial", 20, QFont.Bold))
        section_title.setStyleSheet("""
            color: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #8B5CF6,
                stop:0.5 #EC4899,
                stop:1 #F59E0B);
            background: transparent;
            padding: 10px;
        """)
        section_title.setAlignment(Qt.AlignCenter)
        stats_layout.addWidget(section_title)

        # شبكة الإحصائيات - مسافات مضبوطة
        stats_grid = QHBoxLayout()
        stats_grid.setSpacing(10)  # مسافات مضبوطة بين البطاقات

        # إحصائيات متطورة مع أيقونات وعناوين محسنة
        ultra_stats = [
            ("💎", "إجمالي الإيرادات المتميزة", "2,450,000 جنيه", "#27ae60", "+15.2%"),
            ("🚀", "قاعدة العملاء النشطة", "1,247 عميل", "#3498db", "+8.7%"),
            ("🔥", "المشاريع الحيوية", "89 مشروع", "#e74c3c", "+12.4%"),
            ("⚡", "معدل الإنجاز السريع", "94.2%", "#9b59b6", "+2.1%"),
            ("🌟", "التقييم الاستثنائي", "ممتاز", "#f39c12", "+5.8%")
        ]

        for icon, title, value, color, growth in ultra_stats:
            stat_card = self.create_ultra_stat_card(icon, title, value, color, growth)
            stats_grid.addWidget(stat_card)

        stats_layout.addLayout(stats_grid)
        stats_frame.setLayout(stats_layout)
        return stats_frame

    def create_ultra_stat_card(self, icon, title, value, color, growth):
        """إنشاء بطاقة إحصائية متطورة بدون إطارات مع ارتفاع موحد"""
        card = QFrame()
        card.setFixedHeight(200)  # زيادة الارتفاع أكثر لتكبير الطول
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.75),
                    stop:0.3 rgba(248, 250, 252, 0.7),
                    stop:0.5 rgba(226, 232, 240, 0.65),
                    stop:0.7 rgba(203, 213, 225, 0.6),
                    stop:1 rgba(148, 163, 184, 0.55));
                border: none;
                border-radius: 25px;
                margin: 2px;
                box-shadow: 0 10px 30px rgba(15, 23, 42, 0.4),
                           0 4px 15px rgba(37, 99, 235, 0.3),
                           inset 0 1px 0 rgba(255, 255, 255, 0.6);
            }}
            QFrame:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 1.0),
                    stop:0.3 rgba(255, 255, 255, 1.0),
                    stop:0.5 rgba(255, 255, 255, 1.0),
                    stop:0.7 rgba(255, 255, 255, 1.0),
                    stop:1 rgba(255, 255, 255, 1.0));
                border: none;
                transform: translateY(-5px) scale(1.02);
                box-shadow: 0 20px 50px rgba(15, 23, 42, 0.5),
                           0 8px 25px rgba(37, 99, 235, 0.6),
                           inset 0 1px 0 rgba(255, 255, 255, 1.0);
            }}
        """)

        card_layout = QVBoxLayout()
        card_layout.setContentsMargins(12, 12, 12, 12)  # هوامش أكبر للارتفاع الجديد
        card_layout.setSpacing(10)  # مسافات أكبر بين العناصر

        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setFont(QFont("Segoe UI", 38, QFont.Bold))
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet(f"""
            color: {color};
            background: transparent;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        """)
        card_layout.addWidget(icon_label)

        # القيمة الرئيسية
        value_label = QLabel(value)
        value_label.setFont(QFont("Segoe UI", 22, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"""
            color: {color};
            background: transparent;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
        """)
        card_layout.addWidget(value_label)

        # العنوان
        title_label = QLabel(title)
        title_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            color: #1e293b;
            background: transparent;
            font-weight: bold;
        """)
        title_label.setWordWrap(True)
        card_layout.addWidget(title_label)

        # مؤشر النمو
        growth_label = QLabel(f"📈 {growth}")
        growth_label.setFont(QFont("Segoe UI", 13, QFont.Bold))
        growth_label.setAlignment(Qt.AlignCenter)
        growth_label.setStyleSheet("""
            color: #059669;
            background: transparent;
            font-weight: bold;
        """)
        card_layout.addWidget(growth_label)

        card.setLayout(card_layout)
        return card

    def create_ultra_charts_section(self):
        """إنشاء قسم الرسوم البيانية المتطور بدون إطارات"""
        charts_frame = QFrame()
        charts_frame.setStyleSheet("""
            QFrame {
                background: transparent;
                border: none;
            }
        """)

        charts_layout = QVBoxLayout()
        charts_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش لاستغلال الفراغ
        charts_layout.setSpacing(8)  # مسافات مقللة بين المكونات

        # عنوان القسم المتطور مع أيقونة محسنة
        section_title = QLabel("🚀 البيانات التفاعلية والتحليلات الديناميكية")
        section_title.setFont(QFont("Arial", 20, QFont.Bold))
        section_title.setStyleSheet("""
            color: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #3B82F6,
                stop:0.5 #8B5CF6,
                stop:1 #EC4899);
            background: transparent;
            padding: 10px;
        """)
        section_title.setAlignment(Qt.AlignCenter)
        charts_layout.addWidget(section_title)

        # رسم بياني شامل متطور
        mega_chart = self.create_mega_chart()
        charts_layout.addWidget(mega_chart)

        charts_frame.setLayout(charts_layout)
        return charts_frame

    def create_mega_chart(self):
        """إنشاء رسم بياني شامل متطور بدون إطارات"""
        chart_frame = QFrame()
        chart_frame.setStyleSheet("""
            QFrame {
                background: transparent;
                border: none;
            }
        """)

        chart_layout = QVBoxLayout()
        chart_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش لاستغلال الفراغ
        chart_layout.setSpacing(6)  # مسافات مقللة بين الصفوف



        # الصف الأول: الإيرادات والمصروفات - مسافات مضبوطة
        financial_row = QHBoxLayout()
        financial_row.setSpacing(10)  # مسافات مضبوطة بين البطاقات

        # بيانات مالية متطورة مع أيقونات وعناوين محسنة
        financial_data = [
            ("💎 الإيرادات الذهبية", "2,450,000", "#27ae60", 85),
            ("🔥 المصروفات الحيوية", "1,680,000", "#e74c3c", 65),
            ("🚀 الأرباح المتسارعة", "770,000", "#3498db", 75),
            ("⚡ الاستثمارات الذكية", "320,000", "#9b59b6", 45)
        ]

        for title, value, color, percentage in financial_data:
            financial_card = self.create_advanced_data_card(title, value, color, percentage)
            financial_row.addWidget(financial_card)

        chart_layout.addLayout(financial_row)

        # الصف الثاني: العملاء والمشاريع - مسافات مضبوطة
        business_row = QHBoxLayout()
        business_row.setSpacing(10)  # مسافات مضبوطة بين البطاقات

        business_data = [
            ("🌟 العملاء المتميزين", "1,247", "#3498db", 92),
            ("🔥 المشاريع النشطة", "89", "#f39c12", 78),
            ("💫 المشاريع المنجزة", "156", "#27ae60", 95),
            ("⚡ المشاريع العاجلة", "12", "#e74c3c", 15)
        ]

        for title, value, color, percentage in business_data:
            business_card = self.create_advanced_data_card(title, value, color, percentage)
            business_row.addWidget(business_card)

        chart_layout.addLayout(business_row)

        # الصف الثالث: بيانات إضافية - مسافات مضبوطة
        additional_row = QHBoxLayout()
        additional_row.setSpacing(10)  # مسافات مضبوطة بين البطاقات

        additional_data = [
            ("🚀 التقارير المتقدمة", "245", "#9b59b6", 88),
            ("💎 العقود الذهبية", "67", "#e67e22", 72),
            ("🌟 الإنجازات المتميزة", "34", "#2ecc71", 95),
            ("⚡ المهام السريعة", "18", "#e74c3c", 25)
        ]

        for title, value, color, percentage in additional_data:
            additional_card = self.create_advanced_data_card(title, value, color, percentage)
            additional_row.addWidget(additional_card)

        chart_layout.addLayout(additional_row)

        chart_frame.setLayout(chart_layout)
        return chart_frame

    def create_advanced_data_card(self, title, value, color, percentage):
        """إنشاء بطاقة بيانات متطورة بدون إطارات مع ارتفاع موحد"""
        card = QFrame()
        card.setFixedHeight(118)  # إنزال بدرجتين (120 - 2 = 118)
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.7),
                    stop:0.3 rgba(248, 250, 252, 0.65),
                    stop:0.5 rgba(226, 232, 240, 0.6),
                    stop:0.7 rgba(203, 213, 225, 0.55),
                    stop:1 rgba(148, 163, 184, 0.5));
                border: none;
                border-radius: 18px;
                margin: 1px;
                box-shadow: 0 6px 20px rgba(15, 23, 42, 0.35),
                           0 2px 10px rgba(37, 99, 235, 0.3),
                           inset 0 1px 0 rgba(255, 255, 255, 0.5);
            }}
            QFrame:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 1.0),
                    stop:0.3 rgba(255, 255, 255, 1.0),
                    stop:0.5 rgba(255, 255, 255, 1.0),
                    stop:0.7 rgba(255, 255, 255, 1.0),
                    stop:1 rgba(255, 255, 255, 1.0));
                border: none;
                transform: translateY(-3px) scale(1.01);
                box-shadow: 0 12px 30px rgba(15, 23, 42, 0.4),
                           0 5px 15px rgba(37, 99, 235, 0.5),
                           inset 0 1px 0 rgba(255, 255, 255, 1.0);
            }}
        """)

        card_layout = QVBoxLayout()
        card_layout.setContentsMargins(1, 1, 1, 1)  # أقل هوامش ممكنة لاستغلال المساحة
        card_layout.setSpacing(1)  # أقل مسافات ممكنة لاستغلال المساحة العمودية

        # العنوان
        title_label = QLabel(title)
        title_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"""
            color: {color};
            background: transparent;
            font-weight: bold;
        """)
        card_layout.addWidget(title_label)

        # القيمة
        value_label = QLabel(value)
        value_label.setFont(QFont("Segoe UI", 20, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet("""
            color: #1e293b;
            background: transparent;
            font-weight: bold;
        """)
        card_layout.addWidget(value_label)

        # شريط التقدم المتطور
        progress = QProgressBar()
        progress.setRange(0, 100)
        progress.setValue(percentage)
        progress.setFixedHeight(12)
        progress.setTextVisible(False)
        progress.setStyleSheet(f"""
            QProgressBar {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(226, 232, 240, 0.8),
                    stop:1 rgba(203, 213, 225, 0.9));
                border: 1px solid rgba(148, 163, 184, 0.5);
                border-radius: 6px;
            }}
            QProgressBar::chunk {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {color},
                    stop:0.5 rgba(255, 255, 255, 0.3),
                    stop:1 {color});
                border-radius: 5px;
            }}
        """)
        card_layout.addWidget(progress)

        # النسبة المئوية
        percentage_label = QLabel(f"{percentage}%")
        percentage_label.setFont(QFont("Segoe UI", 10, QFont.Bold))
        percentage_label.setAlignment(Qt.AlignCenter)
        percentage_label.setStyleSheet(f"""
            color: {color};
            background: transparent;
            font-weight: bold;
        """)
        card_layout.addWidget(percentage_label)

        card.setLayout(card_layout)
        return card

    def apply_elegant_styling(self):
        """تطبيق التصميم الأنيق"""
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #475569, stop:0.8 #334155,
                    stop:0.9 #1E293B, stop:1 #0F172A);
                color: #ffffff;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
        """)
